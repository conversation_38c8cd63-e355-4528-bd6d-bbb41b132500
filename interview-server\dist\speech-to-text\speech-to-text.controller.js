"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SpeechToTextController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeechToTextController = void 0;
const common_1 = require("@nestjs/common");
const speech_to_text_service_1 = require("./speech-to-text.service");
let SpeechToTextController = SpeechToTextController_1 = class SpeechToTextController {
    constructor(speechToTextService) {
        this.speechToTextService = speechToTextService;
        this.logger = new common_1.Logger(SpeechToTextController_1.name);
    }
    async convertSpeechToText(request) {
        try {
            this.logger.log('Received speech-to-text request');
            if (!request.audioBase64) {
                throw new common_1.HttpException('Missing required field: audioBase64 is required', common_1.HttpStatus.BAD_REQUEST);
            }
            const result = await this.speechToTextService.convertSpeechToText(request);
            this.logger.log('Speech-to-text conversion completed successfully');
            return result;
        }
        catch (error) {
            this.logger.error('Speech-to-text conversion failed:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                error: 'Speech-to-text conversion failed',
                details: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.SpeechToTextController = SpeechToTextController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SpeechToTextController.prototype, "convertSpeechToText", null);
exports.SpeechToTextController = SpeechToTextController = SpeechToTextController_1 = __decorate([
    (0, common_1.Controller)('speech-to-text'),
    __metadata("design:paramtypes", [speech_to_text_service_1.SpeechToTextService])
], SpeechToTextController);
//# sourceMappingURL=speech-to-text.controller.js.map