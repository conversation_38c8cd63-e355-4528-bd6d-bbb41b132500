"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SpeechToTextService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeechToTextService = void 0;
const common_1 = require("@nestjs/common");
const speech = require('@google-cloud/speech');
let SpeechToTextService = SpeechToTextService_1 = class SpeechToTextService {
    constructor() {
        this.logger = new common_1.Logger(SpeechToTextService_1.name);
        try {
            const credentials = process.env.GOOGLE_APPLICATION_CREDENTIALS
                ? { keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS }
                : { keyFilename: './googleCloud.json' };
            this.client = new speech.SpeechClient(credentials);
            this.logger.log('Google Cloud Speech client initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Google Cloud Speech client:', error);
            this.logger.error('Make sure googleCloud.json exists in the interview-server directory or set GOOGLE_APPLICATION_CREDENTIALS environment variable');
            throw new common_1.HttpException('Speech service initialization failed', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async convertSpeechToText(request) {
        try {
            const { audioBase64, encoding = 'WEBM_OPUS', sampleRateHertz = 48000, languageCode = 'en-US' } = request;
            if (!audioBase64) {
                throw new common_1.HttpException('No audio data provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const audioBuffer = Buffer.from(audioBase64, 'base64');
            const audioBytes = audioBuffer.toString('base64');
            const audioConfig = {
                content: audioBytes,
            };
            const config = {
                encoding: encoding,
                sampleRateHertz: sampleRateHertz,
                languageCode: languageCode,
                enableAutomaticPunctuation: true,
                model: 'latest_long',
                useEnhanced: true,
            };
            const recognitionRequest = {
                audio: audioConfig,
                config: config,
            };
            this.logger.log('Starting speech recognition...');
            const [response] = await this.client.recognize(recognitionRequest);
            if (!response.results || response.results.length === 0) {
                this.logger.warn('No speech detected in audio');
                return {
                    transcription: '',
                    confidence: 0
                };
            }
            const transcription = response.results
                .map((result) => result.alternatives[0].transcript)
                .join('\n');
            const confidence = response.results[0]?.alternatives[0]?.confidence || 0;
            this.logger.log(`Speech recognition completed. Transcription: "${transcription}"`);
            return {
                transcription,
                confidence
            };
        }
        catch (error) {
            this.logger.error('Speech recognition error:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException({
                error: 'Speech recognition failed',
                details: error.message
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.SpeechToTextService = SpeechToTextService;
exports.SpeechToTextService = SpeechToTextService = SpeechToTextService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], SpeechToTextService);
//# sourceMappingURL=speech-to-text.service.js.map