"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeechToTextModule = void 0;
const common_1 = require("@nestjs/common");
const speech_to_text_controller_1 = require("./speech-to-text.controller");
const speech_to_text_service_1 = require("./speech-to-text.service");
let SpeechToTextModule = class SpeechToTextModule {
};
exports.SpeechToTextModule = SpeechToTextModule;
exports.SpeechToTextModule = SpeechToTextModule = __decorate([
    (0, common_1.Module)({
        controllers: [speech_to_text_controller_1.SpeechToTextController],
        providers: [speech_to_text_service_1.SpeechToTextService],
        exports: [speech_to_text_service_1.SpeechToTextService],
    })
], SpeechToTextModule);
//# sourceMappingURL=speech-to-text.module.js.map