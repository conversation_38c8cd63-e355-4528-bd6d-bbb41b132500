"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors({
        origin: [
            'http://localhost:3000',
            'http://localhost:3001',
            'https://intview-ai.vercel.app',
            /\.vercel\.app$/
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        credentials: true,
    });
    const port = process.env.PORT || 4000;
    await app.listen(port);
    console.log(`Interview Server is running on port ${port}`);
}
bootstrap();
//# sourceMappingURL=main.js.map