import { SpeechToTextService } from './speech-to-text.service';
import { SpeechToTextRequest, SpeechToTextResponse } from './interfaces/speech-to-text.interface';
export declare class SpeechToTextController {
    private readonly speechToTextService;
    private readonly logger;
    constructor(speechToTextService: SpeechToTextService);
    convertSpeechToText(request: SpeechToTextRequest): Promise<SpeechToTextResponse>;
}
