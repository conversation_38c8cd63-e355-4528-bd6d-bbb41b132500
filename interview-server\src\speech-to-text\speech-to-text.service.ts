import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { SpeechToTextRequest, SpeechToTextResponse } from './interfaces/speech-to-text.interface';

// Import Google Cloud Speech client
const speech = require('@google-cloud/speech');

@Injectable()
export class SpeechToTextService {
  private readonly logger = new Logger(SpeechToTextService.name);
  private client: any;

  constructor() {
    try {
      // Initialize Google Cloud Speech client
      // Try environment variable first, then fall back to key file
      const credentials = process.env.GOOGLE_APPLICATION_CREDENTIALS
        ? { keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS }
        : { keyFilename: './googleCloud.json' };

      this.client = new speech.SpeechClient(credentials);

      this.logger.log('Google Cloud Speech client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Google Cloud Speech client:', error);
      this.logger.error('Make sure googleCloud.json exists in the interview-server directory or set GOOGLE_APPLICATION_CREDENTIALS environment variable');
      throw new HttpException(
        'Speech service initialization failed',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async convertSpeechToText(request: SpeechToTextRequest): Promise<SpeechToTextResponse> {
    try {
      const { audioBase64, encoding = 'WEBM_OPUS', sampleRateHertz = 48000, languageCode = 'en-US' } = request;

      if (!audioBase64) {
        throw new HttpException('No audio data provided', HttpStatus.BAD_REQUEST);
      }

      // Convert base64 audio to buffer
      const audioBuffer = Buffer.from(audioBase64, 'base64');
      const audioBytes = audioBuffer.toString('base64');

      // Configure the audio settings
      const audioConfig = {
        content: audioBytes,
      };

      // Configure the recognition settings
      const config = {
        encoding: encoding,
        sampleRateHertz: sampleRateHertz,
        languageCode: languageCode,
        enableAutomaticPunctuation: true,
        model: 'latest_long', // Use latest model for better accuracy
        useEnhanced: true, // Use enhanced model if available
      };

      const recognitionRequest = {
        audio: audioConfig,
        config: config,
      };

      this.logger.log('Starting speech recognition...');

      // Perform the speech recognition
      const [response] = await this.client.recognize(recognitionRequest);
      
      if (!response.results || response.results.length === 0) {
        this.logger.warn('No speech detected in audio');
        return {
          transcription: '',
          confidence: 0
        };
      }

      // Extract transcription from results
      const transcription = response.results
        .map((result: any) => result.alternatives[0].transcript)
        .join('\n');

      // Get confidence score from the first result
      const confidence = response.results[0]?.alternatives[0]?.confidence || 0;

      this.logger.log(`Speech recognition completed. Transcription: "${transcription}"`);

      return {
        transcription,
        confidence
      };

    } catch (error) {
      this.logger.error('Speech recognition error:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        {
          error: 'Speech recognition failed',
          details: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
