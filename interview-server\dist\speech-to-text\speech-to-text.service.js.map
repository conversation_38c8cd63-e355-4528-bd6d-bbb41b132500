{"version": 3, "file": "speech-to-text.service.js", "sourceRoot": "", "sources": ["../../src/speech-to-text/speech-to-text.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAA+E;AAI/E,MAAM,MAAM,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAGxC,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAI9B;QAHiB,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QAI7D,IAAI,CAAC;YAGH,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,8BAA8B;gBAC5D,CAAC,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBAC7D,CAAC,CAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;YAE1C,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gIAAgI,CAAC,CAAC;YACpJ,MAAM,IAAI,sBAAa,CACrB,sCAAsC,EACtC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAA4B;QACpD,IAAI,CAAC;YACH,MAAM,EAAE,WAAW,EAAE,QAAQ,GAAG,WAAW,EAAE,eAAe,GAAG,KAAK,EAAE,YAAY,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;YAEzG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,sBAAa,CAAC,wBAAwB,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;YAC5E,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGlD,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,UAAU;aACpB,CAAC;YAGF,MAAM,MAAM,GAAG;gBACb,QAAQ,EAAE,QAAQ;gBAClB,eAAe,EAAE,eAAe;gBAChC,YAAY,EAAE,YAAY;gBAC1B,0BAA0B,EAAE,IAAI;gBAChC,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,MAAM,kBAAkB,GAAG;gBACzB,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,MAAM;aACf,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAGlD,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAEnE,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAChD,OAAO;oBACL,aAAa,EAAE,EAAE;oBACjB,UAAU,EAAE,CAAC;iBACd,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO;iBACnC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;iBACvD,IAAI,CAAC,IAAI,CAAC,CAAC;YAGd,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,aAAa,GAAG,CAAC,CAAC;YAEnF,OAAO;gBACL,aAAa;gBACb,UAAU;aACX,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAEtD,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,sBAAa,CACrB;gBACE,KAAK,EAAE,2BAA2B;gBAClC,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,EACD,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AArGY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;;GACA,mBAAmB,CAqG/B"}