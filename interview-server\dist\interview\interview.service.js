"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InterviewService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const generative_ai_1 = require("@google/generative-ai");
let InterviewService = class InterviewService {
    constructor(configService) {
        this.configService = configService;
        const apiKey = this.configService.get('GEMINI_API_KEY');
        if (!apiKey) {
            throw new Error('GEMINI_API_KEY environment variable is not set');
        }
        console.log('Initializing Gemini with API key:', apiKey.substring(0, 10) + '...');
        this.gemini = new generative_ai_1.GoogleGenerativeAI(apiKey);
    }
    async runInterview(interviewData) {
        const { position, name, experience, history } = interviewData;
        const formattedHistory = history.length > 0
            ? history.map(msg => `${msg.role === 'candidate' ? 'Candidate' : 'Interviewer'}: ${msg.content}`).join('\n')
            : 'No previous conversation.';
        const prompt = `
You are a professional technical interviewer. You must respond with valid JSON only.

Details:
- Position: ${position}
- Candidate Name: ${name}
- Experience: ${experience} years
- Interview Style: Conversational, professional, encouraging
- Keep it role-specific and relevant to their experience level.

Interview Rules:
1. Ask one question at a time.
2. Adapt difficulty based on candidate's answers.
3. Start with a simple warm-up question.
4. Avoid yes/no questions — ask open-ended ones.
5. After each answer, evaluate briefly (score 0–20 points per question).
6. Ask exactly 5 questions total covering:
   - Question 1: Technical skills (React, JavaScript, HTML/CSS) - 0-20 points
   - Question 2: Problem-solving - 0-20 points
   - Question 3: Past project experience - 0-20 points
   - Question 4: Soft skills/teamwork - 0-20 points
   - Question 5: Industry knowledge - 0-20 points
7. Total possible score is 100 points (5 questions × 20 points each).
8. At the end, provide a complete summary where:
   - Each ScoreCard category shows the score from its corresponding question
   - Overall score is the sum of all 5 individual question scores
   - Keep track of which question corresponds to which category

Conversation so far:
${formattedHistory}

IMPORTANT: You must respond with ONLY valid JSON in this exact format:

If the interview is NOT complete:
{
  "nextQuestion": "Your next interview question here",
  "currentQuestionScore": 0,
  "isInterviewCompleted": false
}

If the interview IS complete (after asking sufficient questions):
{
  "nextQuestion": "",
  "currentQuestionScore": [score for last answer 0-20],
  "isInterviewCompleted": true,
  "Summary": {
    "ScoreCard": {
      "technicalSkills": [score from technical question 0-20],
      "problemSolving": [score from problem-solving question 0-20],
      "communication": [score from communication question 0-20],
      "experience": [score from experience question 0-20],
      "overall": [sum of all 5 question scores 0-100]
    },
    "recommendation": "HIRE/REJECT/CONSIDER",
    "reason": "Detailed explanation of the hiring decision based on performance"
  }
}

CRITICAL: Respond with ONLY the raw JSON object. Do NOT wrap it in markdown code blocks or any other formatting. Do NOT include \`\`\`json or \`\`\` or any other text. Just the pure JSON object.
`;
        try {
            const model = this.gemini.getGenerativeModel({ model: 'gemini-2.0-flash' });
            const result = await model.generateContent(prompt);
            const responseText = result.response.text();
            try {
                let jsonText = responseText.trim();
                if (jsonText.startsWith('```json')) {
                    jsonText = jsonText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                }
                else if (jsonText.startsWith('```')) {
                    jsonText = jsonText.replace(/^```\s*/, '').replace(/\s*```$/, '');
                }
                const parsedResponse = JSON.parse(jsonText);
                if (typeof parsedResponse.nextQuestion !== 'string' ||
                    typeof parsedResponse.currentQuestionScore !== 'number' ||
                    typeof parsedResponse.isInterviewCompleted !== 'boolean') {
                    throw new Error('Invalid response structure from Gemini API');
                }
                if (parsedResponse.currentQuestionScore < 0 || parsedResponse.currentQuestionScore > 20) {
                    console.warn(`Invalid question score: ${parsedResponse.currentQuestionScore}, clamping to 0-20 range`);
                    parsedResponse.currentQuestionScore = Math.max(0, Math.min(20, parsedResponse.currentQuestionScore));
                }
                if (parsedResponse.Summary && parsedResponse.Summary.ScoreCard) {
                    const scoreCard = parsedResponse.Summary.ScoreCard;
                    const categories = ['technicalSkills', 'problemSolving', 'communication', 'experience'];
                    categories.forEach(category => {
                        if (scoreCard[category] < 0 || scoreCard[category] > 20) {
                            console.warn(`Invalid ${category} score: ${scoreCard[category]}, clamping to 0-20 range`);
                            scoreCard[category] = Math.max(0, Math.min(20, scoreCard[category]));
                        }
                    });
                    if (scoreCard.overall < 0 || scoreCard.overall > 100) {
                        console.warn(`Invalid overall score: ${scoreCard.overall}, clamping to 0-100 range`);
                        scoreCard.overall = Math.max(0, Math.min(100, scoreCard.overall));
                    }
                }
                console.log('Parsed interview response:', JSON.stringify(parsedResponse, null, 2));
                return parsedResponse;
            }
            catch (parseError) {
                console.error('Failed to parse JSON response:', responseText);
                console.error('Parse error:', parseError);
                let fallbackQuestion = responseText;
                const questionMatch = responseText.match(/"nextQuestion":\s*"([^"]+)"/);
                if (questionMatch) {
                    fallbackQuestion = questionMatch[1];
                }
                return {
                    nextQuestion: fallbackQuestion,
                    currentQuestionScore: 0,
                    isInterviewCompleted: false
                };
            }
        }
        catch (error) {
            console.error('Gemini API Error:', error);
            if (error.message?.includes('API key not valid')) {
                throw new Error('Invalid Gemini API key. Please check your GEMINI_API_KEY environment variable.');
            }
            else if (error.message?.includes('quota')) {
                throw new Error('Gemini API quota exceeded. Please check your usage limits.');
            }
            else {
                throw new Error(`Failed to generate interview response: ${error.message || 'Unknown error'}`);
            }
        }
    }
};
exports.InterviewService = InterviewService;
exports.InterviewService = InterviewService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], InterviewService);
//# sourceMappingURL=interview.service.js.map